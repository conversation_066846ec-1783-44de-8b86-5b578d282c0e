import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { animate, style, transition, trigger } from '@angular/animations';
import {
  CurrencyPipe,
  DatePipe,
  isPlatformBrowser,
  NgClass,
  NgForOf,
  NgIf,
} from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Proposal } from '../../../lib/models';
import { ActivatedRoute } from '@angular/router';
import { Meta, Title } from '@angular/platform-browser';
import { ProposalService } from '../../services';

@Component({
  selector: 'chm-proposal-details',
  imports: [CurrencyPipe, DatePipe, FormsModule, NgForOf, NgIf, NgClass],
  templateUrl: './proposal-details.component.html',
  styleUrl: './proposal-details.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(100px)' }),
        animate('600ms ease-out', style({ opacity: 1, transform: 'none' })),
      ]),
    ]),
  ],
})
export class ProposalDetailsComponent implements OnInit {
  currentYear = new Date().getFullYear();
  showSidebar = false;
  agreed = false;
  proposal!: Proposal;

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private route: ActivatedRoute,
    private titleService: Title,
    private metaService: Meta,
    private proposalService: ProposalService,
  ) {}

  // Get initial phase items
  get initialPhaseItems() {
    return (
      this.proposal?.pricing?.filter(
        (item) => !item.added_after_initial_phase,
      ) || []
    );
  }

  // Get overtime items
  get overtimeItems() {
    return (
      this.proposal?.pricing?.filter(
        (item) => item.added_after_initial_phase,
      ) || []
    );
  }

  // Get initial phase total with 2 decimal precision
  get initialPhaseTotal() {
    const total = this.initialPhaseItems.reduce(
      (sum, item) => sum + (item.total || 0),
      0,
    );
    return Math.round(total * 100) / 100;
  }

  // Get overtime total with 2 decimal precision
  get overtimeTotal() {
    const total = this.overtimeItems.reduce(
      (sum, item) => sum + (item.total || 0),
      0,
    );
    return Math.round(total * 100) / 100;
  }

  // Get Phase 1 amount based on payment type
  getPhase1Amount(): number {
    console.log(this.proposal.payment_type);

    if (this.proposal.payment_type === 'full') {
      return this.proposal.price;
    }
    return this.proposal.initial_payment || this.proposal.price * 0.5;
  }

  // Get Phase 2 amount based on payment type
  getPhase2Amount(): number {
    if (this.proposal.payment_type === 'full') {
      return 0; // No second phase for full payment
    }
    return this.proposal.initial_payment
      ? this.proposal.price - this.proposal.initial_payment
      : this.proposal.price * 0.5;
  }

  // Get payment button text based on payment type and status
  getPaymentButtonText(): string {
    if (this.proposal.status === 'paid') {
      return 'Paid';
    }

    if (this.proposal.payment_type === 'full') {
      return 'Accept & Pay';
    }

    // Split payment logic
    if (this.proposal.status === 'partially_paid') {
      return 'Pay Phase 2';
    }

    return 'Accept & Pay Phase 1';
  }

  // Get initial payment button text for terms section
  getInitialPaymentButtonText(): string {
    if (this.proposal.payment_type === 'full') {
      return 'Accept & Pay';
    }

    return 'Accept & Pay Phase 1';
  }

  ngOnInit() {
    this.proposal = this.route.snapshot.data['proposal'];

    this.setMetadata();
  }

  onScroll(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    // Show sidebar based on scroll position for unpaid proposals
    if (
      !this.showSidebar &&
      scrollTop + windowHeight >= documentHeight - 2000 &&
      this.proposal.status !== 'paid'
    ) {
      this.showSidebar = true;
    }

    // Always show sidebar for partially paid (split payment in progress)
    if (this.proposal.status === 'partially_paid') {
      this.showSidebar = true;
    }
  }

  redirectToStripe(): void {
    if (!this.proposal?.id) return;

    this.proposalService.pay(this.proposal.id).subscribe((response) => {
      window.location.href = response.url;
    });
  }

  downloadPdf(): void {
    if (isPlatformBrowser(this.platformId)) {
      window.print();
    }
  }

  downloadInvoice(type: 'initial' | 'final'): void {
    if (!this.proposal?.id) return;

    this.proposalService.downloadInvoice(this.proposal.id, type).subscribe({
      next: (blob) => {
        // Create a download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Generate filename based on payment type
        let invoiceType: string;
        if (this.proposal.payment_type === 'full') {
          invoiceType = 'Invoice';
        } else {
          invoiceType = type === 'initial' ? 'Phase-1' : 'Phase-2';
        }

        const filename = `${this.proposal.project_title}-${invoiceType}.pdf`;
        link.download = filename;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading invoice:', error);
        // You could add a toast notification here
        alert('Failed to download invoice. Please try again.');
      },
    });
  }

  private setMetadata() {
    const title = this.proposal?.project_title
      ? `${this.proposal.project_title} – Chainmatic Proposal`
      : 'Proposal – Chainmatic';

    this.titleService.setTitle(title);

    const description = `Proposal for ${this.proposal.client.name} – ${this.proposal.project_title} by Chainmatic. View scope, pricing, and timeline.`;

    this.metaService.updateTag({
      name: 'description',
      content: description,
    });
    this.metaService.updateTag({ property: 'og:title', content: title });
    this.metaService.updateTag({
      property: 'og:description',
      content: description,
    });
    this.metaService.updateTag({
      property: 'og:url',
      content: `https://proposals.chainmatic.ai/p/${this.proposal.id}`,
    });
  }
}
