import { Component, OnInit } from '@angular/core';
import { Textarea } from 'primeng/textarea';
import { But<PERSON> } from 'primeng/button';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Client, Proposal } from '../../../lib/models';
import { Select } from 'primeng/select';
import { Dialog } from 'primeng/dialog';
import { InputText } from 'primeng/inputtext';
import { ProgressSpinner } from 'primeng/progressspinner';
import { DatePicker } from 'primeng/datepicker';
import { ClientService, ProposalService } from '../../services';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CurrencyPipe, NgForOf, NgIf } from '@angular/common';
import { ProposalDraft, ProposalRequest } from '../../../lib/validators';
import { addDays, format } from 'date-fns';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

@Component({
  selector: 'chm-proposal-editor',
  imports: [
    Textarea,
    FormsModule,
    Button,
    Select,
    Dialog,
    InputText,
    ProgressSpinner,
    DatePicker,
    ReactiveFormsModule,
    NgForOf,
    NgIf,
    RouterLink,
    CurrencyPipe,
  ],
  templateUrl: './proposal-editor.component.html',
  styleUrl: './proposal-editor.component.css',
  standalone: true,
})
export class ProposalEditorComponent implements OnInit {
  selectedClient?: Client;
  clients: Client[] = [];
  clientDialogVisible = false;
  editMode = false;
  clientForm!: FormGroup;
  generateProposalFormGroup!: FormGroup;
  proposalDraft?: ProposalDraft;
  isGeneratingDraft = false;
  isCreatingProposal = false;
  isEditMode = false;
  isLoadingProposal = false;
  currentProposal?: Proposal;
  proposalId?: string;

  // Payment type options
  paymentTypeOptions = [
    {
      label: 'Split Payment',
      value: 'split',
      description: 'Initial payment + Final payment (recommended)',
    },
    {
      label: 'Full Payment',
      value: 'full',
      description: 'Single payment for the entire project',
    },
  ];

  constructor(
    private fb: FormBuilder,
    private clientService: ClientService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private proposalService: ProposalService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    this.clientForm = this.fb.group({
      name: [null, Validators.required],
      email: [null, [Validators.required, Validators.email]],
      rep_full_name: [null, Validators.required],
    });

    this.generateProposalFormGroup = this.fb.group({
      client_id: [null, Validators.required],
      hourly_rate: [59.77, Validators.required],
      currency: ['BGN', Validators.required],
      expiration_date: [
        format(addDays(new Date(), 14), 'yyyy-MM-dd'),
        Validators.required,
      ],
      prompt: [null, Validators.required],
      current_draft: [null],
      payment_type: ['split', Validators.required],
    });
  }

  // Get initial phase items
  get initialPhaseItems() {
    return (
      this.proposalDraft?.pricing?.filter(
        (item) => !item.added_after_initial_phase,
      ) || []
    );
  }

  // Get overtime items
  get overtimeItems() {
    return (
      this.proposalDraft?.pricing?.filter(
        (item) => item.added_after_initial_phase,
      ) || []
    );
  }

  // Get initial phase total with 2 decimal precision
  get initialPhaseTotal() {
    const total = this.initialPhaseItems.reduce(
      (sum, item) => sum + (item.total || 0),
      0,
    );
    return Math.round(total * 100) / 100;
  }

  // Get overtime total with 2 decimal precision
  get overtimeTotal() {
    const total = this.overtimeItems.reduce(
      (sum, item) => sum + (item.total || 0),
      0,
    );
    return Math.round(total * 100) / 100;
  }

  // Calculate total price from all pricing items with 2 decimal precision
  calculateTotalPrice(): void {
    if (
      !this.proposalDraft ||
      !this.proposalDraft.pricing ||
      this.proposalDraft.pricing.length === 0
    ) {
      if (this.proposalDraft) {
        this.proposalDraft.price = 0;
      }
      return;
    }

    const total = this.proposalDraft.pricing.reduce((sum, item) => {
      return sum + (item.total || 0);
    }, 0);

    // Round to 2 decimal places
    this.proposalDraft.price = Math.round(total * 100) / 100;
  }

  // Calculate item total when hours or rate changes with 2 decimal precision
  calculateItemTotal(item: any): void {
    // Use setTimeout to ensure ngModel has updated the values
    setTimeout(() => {
      const hours = parseFloat(item.hours) || 0;
      const rate = parseFloat(item.rate) || 0;

      // Calculate and round to 2 decimal places
      item.total = Math.round(hours * rate * 100) / 100;

      this.calculateTotalPrice();
    }, 0);
  }

  // Recalculate all item totals and overall price
  recalculateAllPricing(): void {
    if (!this.proposalDraft || !this.proposalDraft.pricing) return;

    // Recalculate each item total
    this.proposalDraft.pricing.forEach((item) => {
      const hours = parseFloat('' + item.hours) || 0;
      const rate = parseFloat('' + item.rate) || 0;
      item.total = Math.round(hours * rate * 100) / 100;
    });

    // Recalculate total price
    this.calculateTotalPrice();
  }

  // Add new pricing item
  addPricingItem(isOvertime: boolean = false): void {
    if (!this.proposalDraft) return;

    const newItem = {
      item: '',
      hours: 0,
      rate: this.generateProposalFormGroup.get('hourly_rate')?.value || 0,
      total: 0,
      added_after_initial_phase: isOvertime,
    };

    if (!this.proposalDraft.pricing) {
      this.proposalDraft.pricing = [];
    }

    this.proposalDraft.pricing.push(newItem);
  }

  // Remove pricing item
  removePricingItem(index: number): void {
    if (!this.proposalDraft || !this.proposalDraft.pricing) return;

    this.proposalDraft.pricing.splice(index, 1);
    this.calculateTotalPrice();
  }

  // Get payment type icon
  getPaymentTypeIcon(paymentType: string): string {
    switch (paymentType) {
      case 'split':
        return 'pi pi-credit-card';
      case 'full':
        return 'pi pi-wallet';
      default:
        return 'pi pi-credit-card';
    }
  }

  // Get payment type label
  getPaymentTypeLabel(paymentType: string): string {
    const option = this.paymentTypeOptions.find(
      (opt) => opt.value === paymentType,
    );
    return option ? option.label : 'Split Payment';
  }

  handleGenerateProposalDraft() {
    this.isGeneratingDraft = true;

    // Prepare form data with current draft if in edit mode
    const formData = { ...this.generateProposalFormGroup.value };
    if (this.isEditMode && this.proposalDraft) {
      formData.current_draft = this.proposalDraft;
    }

    this.proposalService
      .createDraft(formData)
      .subscribe({
        next: (proposalDraft) => {
          this.proposalDraft = proposalDraft;

          // Recalculate all pricing to ensure accuracy
          this.recalculateAllPricing();

          this.generateProposalFormGroup.patchValue({
            current_draft: proposalDraft,
          });
          this.isGeneratingDraft = false;
        },
        error: (error) => {
          console.error('Error generating proposal draft:', error);
          this.isGeneratingDraft = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to generate proposal draft. Please try again.',
          });
        },
      });
  }

  ngOnInit() {
    this.setupClientChangeSubscription();

    // Check if we're in edit mode based on query parameter
    this.route.queryParams.subscribe((params) => {
      this.proposalId = params['id'];
      this.isEditMode = !!this.proposalId;

      if (this.isEditMode) {
        this.loadProposalForEdit();
      } else {
        this.loadClientsForCreate();
      }
    });
  }

  handleCreateClient() {
    if (this.editMode) {
      this.clientService
        .update(this.selectedClient!.id, this.clientForm.value)
        .subscribe(() => {
          this.clients = this.clients.map((c) => {
            if (c.id === this.selectedClient!.id) {
              return {
                ...c,
                ...this.clientForm.value,
              };
            }

            return c;
          });

          this.selectedClient = {
            ...this.selectedClient!,
            ...this.clientForm.value,
          };
          this.clientDialogVisible = false;
          this.clientForm.reset();
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Client edited successfully',
          });

          this.editMode = false;
        });

      return;
    }

    this.clientService.create(this.clientForm.value).subscribe((client) => {
      this.clients = [client, ...this.clients];
      this.selectedClient = client;
      // Update the form control with the new client ID
      this.generateProposalFormGroup.patchValue({
        client_id: client.id,
      });
      this.clientDialogVisible = false;
      this.clientForm.reset();
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Client created successfully',
      });
    });
  }

  handleEditClient() {
    if (!this.selectedClient) return;

    this.clientForm.patchValue({
      name: this.selectedClient.name,
      email: this.selectedClient.email,
      rep_full_name: this.selectedClient.rep_full_name,
    });

    this.clientDialogVisible = true;
    this.editMode = true;
  }

  handleDeleteClient() {
    if (!this.selectedClient) return;

    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${this.selectedClient.name}?`,
      accept: () => {
        this.clientService.delete(this.selectedClient!.id).subscribe(() => {
          this.clients = this.clients.filter(
            (c) => c.id !== this.selectedClient!.id,
          );
          this.selectedClient = undefined;
          this.messageService.add({
            severity: 'success',
            summary: 'Deleted',
            detail: 'Client deleted',
          });
        });
      },
    });
  }

  handleCreateProposal() {
    if (!this.proposalDraft || !this.selectedClient) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please generate a proposal draft first and select a client.',
      });
      return;
    }

    // Create ProposalRequest from the current form data and proposal draft
    const proposalRequest: ProposalRequest = {
      project_title: this.proposalDraft.project_title,
      client_id: this.generateProposalFormGroup.get('client_id')?.value,
      expiration_date:
        this.generateProposalFormGroup.get('expiration_date')?.value,
      hourly_rate: this.generateProposalFormGroup.get('hourly_rate')?.value,
      price: this.proposalDraft.price,
      currency: this.proposalDraft.currency,
      problems: this.proposalDraft.problems,
      solutions: this.proposalDraft.solutions,
      scope: this.proposalDraft.scope,
      timeline: this.proposalDraft.timeline,
      pricing: this.proposalDraft.pricing,
      summary: this.proposalDraft.summary,
      payment_type: this.generateProposalFormGroup.get('payment_type')?.value,
    };

    this.isCreatingProposal = true;

    if (this.isEditMode && this.proposalId) {
      // Update existing proposal
      this.proposalService.update(this.proposalId, proposalRequest).subscribe({
        next: () => {
          this.isCreatingProposal = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: `Proposal "${proposalRequest.project_title}" updated successfully!`,
          });

          // Navigate back to dashboard
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          console.error('Error updating proposal:', error);
          this.isCreatingProposal = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update proposal. Please try again.',
          });
        },
      });
    } else {
      // Create new proposal
      this.proposalService.create(proposalRequest).subscribe({
        next: (proposal) => {
          this.isCreatingProposal = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: `Proposal "${proposal.project_title}" created successfully!`,
          });

          // Navigate to edit mode with the new proposal ID
          this.router.navigate(['/proposal-editor'], {
            queryParams: { id: proposal.id },
          });
        },
        error: (error) => {
          console.error('Error creating proposal:', error);
          this.isCreatingProposal = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to create proposal. Please try again.',
          });
        },
      });
    }
  }

  private setupClientChangeSubscription() {
    this.generateProposalFormGroup
      .get('client_id')
      ?.valueChanges.subscribe((clientId) => {
        if (clientId) {
          this.selectedClient = this.clients.find(
            (client) => client.id === clientId,
          );
        } else {
          this.selectedClient = undefined;
        }
      });
  }

  private loadClientsForCreate() {
    this.clientService.getAll().subscribe((clients) => {
      this.clients = clients;
      this.selectedClient = clients[0];
      this.generateProposalFormGroup.patchValue({
        client_id: clients[0]?.id,
      });
    });
  }

  private loadProposalForEdit() {
    if (!this.proposalId) return;

    this.isLoadingProposal = true;

    // Load both clients and the specific proposal
    Promise.all([
      this.clientService.getAll().toPromise(),
      this.proposalService.get(this.proposalId).toPromise(),
    ])
      .then(([clients, proposal]) => {
        this.clients = clients || [];
        this.currentProposal = proposal;

        if (proposal) {
          // Set the selected client
          this.selectedClient = proposal.client;

          // Convert proposal to proposal draft format for editing
          this.proposalDraft = {
            project_title: proposal.project_title,
            price: proposal.price,
            currency: proposal.currency,
            problems: proposal.problems || [],
            solutions: proposal.solutions || [],
            scope: proposal.scope || [],
            timeline: proposal.timeline || [],
            pricing: proposal.pricing || [],
            summary: proposal.summary || [],
            billing_information: proposal.client?.billing_information || null,
          };

          // Recalculate all pricing to ensure accuracy
          this.recalculateAllPricing();

          // Populate the form with existing proposal data
          this.generateProposalFormGroup.patchValue({
            client_id: proposal.client?.id,
            hourly_rate: proposal.hourly_rate,
            currency: proposal.currency,
            expiration_date: proposal.expiration_date,
            current_draft: this.proposalDraft,
            prompt: null, // Clear prompt for edit mode
            payment_type: proposal.payment_type || 'split',
          });

          console.log('Loaded proposal for editing:', this.proposalDraft);
        }

        this.isLoadingProposal = false;
      })
      .catch((error) => {
        console.error('Error loading proposal for edit:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load proposal for editing.',
        });
        this.isLoadingProposal = false;
        this.router.navigate(['/dashboard']);
      });
  }
}
