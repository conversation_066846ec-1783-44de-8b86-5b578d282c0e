import {
  InvBgService,
  InvBgV3Client,
  InvBgV3InvoiceItem,
  InvBgV3InvoiceRequest,
  InvBgV3VatInfo,
} from './invbg.service';
import { getSupabaseClient } from './supabase.service';
import { EmailService, InvoiceEmailData } from './email.service';
import { Proposal } from '../../lib/models';
import { getCountryMapping } from '../../lib/utils/country-mapping';

export class InvoiceGeneratorService {
  private invBgService: InvBgService;
  private emailService: EmailService;

  constructor() {
    this.invBgService = new InvBgService();
    this.emailService = new EmailService();
  }

  /**
   * Pure function to determine if an invoice should be created
   */
  shouldCreateInvoice(proposal: Proposal): {
    shouldCreate: boolean;
    invoiceType: 'initial' | 'final' | null;
    reason?: string;
  } {
    // Check if client has billing information
    if (!proposal.client.billing_information) {
      return {
        shouldCreate: false,
        invoiceType: null,
        reason: 'No billing information available',
      };
    }

    // Handle different payment types
    if (proposal.payment_type === 'full') {
      // For full payment type, create only one invoice when paid
      if (proposal.status === 'paid' && !proposal.final_invoice_id) {
        return {
          shouldCreate: true,
          invoiceType: 'final',
        };
      }
    } else {
      // For split payment type (default behavior)
      if (proposal.status === 'partially_paid' && !proposal.initial_invoice_id) {
        return {
          shouldCreate: true,
          invoiceType: 'initial',
        };
      }

      if (proposal.status === 'paid' && !proposal.final_invoice_id) {
        return {
          shouldCreate: true,
          invoiceType: 'final',
        };
      }
    }

    return {
      shouldCreate: false,
      invoiceType: null,
      reason: 'Invoice already exists or status does not require invoice',
    };
  }

  /**
   * Pure function to build inv.bg v3 invoice data from proposal
   */
  /**
   * Get or create inv.bg client ID for our client
   */
  async getOrCreateInvBgClient(proposal: Proposal): Promise<number> {
    const billing = proposal.client.billing_information!;

    // Check if we already have a mapping
    const { data: existingMapping } = await getSupabaseClient()
      .from('invbg_client_mapping')
      .select('invbg_client_id')
      .eq('client_id', proposal.client.id)
      .single();

    if (existingMapping) {
      return existingMapping.invbg_client_id;
    }

    // Create new client in inv.bg
    const invBgClientData: InvBgV3Client = {
      name: billing.company_name!,
      name_en: billing.company_name!,
      address: billing.address!,
      address_en: billing.address!,
      town: billing.city!,
      town_en: billing.city!,
      country: getCountryMapping(billing.country || 'Bulgaria').bg,
      country_en: getCountryMapping(billing.country || 'Bulgaria').en,
      bulstat: billing.eik || null,
      mol: billing.mol || null,
      vat_number: billing.vat_number || undefined,
      is_reg_vat: !!billing.vat_number,
      is_person: false,
      email: proposal.client.email,
      phone: undefined,
    };

    const invBgClient = await this.invBgService.createClient(invBgClientData);

    // Store the mapping
    await getSupabaseClient().from('invbg_client_mapping').insert({
      client_id: proposal.client.id,
      invbg_client_id: invBgClient.id,
    });

    return invBgClient.id;
  }

  async buildInvoiceData(
    proposal: Proposal,
    invoiceType: 'initial' | 'final',
    invBgClientId: number,
  ): Promise<InvBgV3InvoiceRequest> {
    // Calculate amount based on payment type and invoice type
    let amount: number;

    if (proposal.payment_type === 'full') {
      // For full payment type, the invoice is always for the full amount
      amount = proposal.price;
    } else {
      // For split payment type, calculate based on invoice type
      amount =
        invoiceType === 'initial'
          ? proposal.initial_payment || 0
          : proposal.price - (proposal.initial_payment || 0);
    }

    // Get VAT information from stored VAT data or default to 20%
    const vatInfo: InvBgV3VatInfo = proposal.vat
      ? {
          percent: proposal.vat.percent,
          reason_without: proposal.vat.reason_without || undefined,
        }
      : {
          percent: 20, // Fallback to standard Bulgarian VAT rate
        };

    // Build invoice items for v3 API
    let itemDescription: string;
    if (proposal.payment_type === 'full') {
      itemDescription = `Project ${proposal.proposal_id} - Full Payment`;
    } else {
      itemDescription = `Project ${proposal.proposal_id} - ${invoiceType === 'initial' ? 'Initial Payment' : 'Final Payment'}`;
    }

    const items: InvBgV3InvoiceItem[] = [
      {
        name: itemDescription,
        quantity: 1,
        price: amount,
        quantity_unit: 'бр.',
        vat_percent: vatInfo.percent,
        discount_percent: 0,
      },
    ];

    // Calculate due date (30 days from now)
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    // Prepare base invoice data
    const invoiceData: InvBgV3InvoiceRequest = {
      client: invBgClientId,
      items,
      payment_currency: proposal.currency,
      date: new Date().toISOString().split('T')[0],
      due_date: dueDate.toISOString().split('T')[0],
      language: 'bg',
      payment_method: 'stripe',
      vat: vatInfo,
      status: 'paid',
    };

    // Add currency rate if currency is not BGN
    if (proposal.currency && proposal.currency.toUpperCase() !== 'BGN') {
      try {
        const currencyRateResponse = await this.invBgService.getCurrencyRate(
          proposal.currency,
        );
        invoiceData.currency_rate = currencyRateResponse.rate;
        invoiceData.date_rate = currencyRateResponse.for_date;
      } catch (error: any) {
        console.error(
          `Failed to fetch currency rate for ${proposal.currency}:`,
          error,
        );
        // Continue without currency rate - inv.bg will handle this
        // or throw error if currency rate is mandatory
        throw new Error(
          `Currency rate required for ${proposal.currency} but could not be fetched: ${error.message}`,
        );
      }
    }

    return invoiceData;
  }

  /**
   * Main function to generate invoice for a proposal
   */
  async generateInvoiceForProposal(proposalId: string): Promise<{
    success: boolean;
    invoiceId?: string;
    invoiceType?: 'initial' | 'final';
    message: string;
  }> {
    try {
      // Fetch proposal with client and billing information
      const { data: proposal, error } = await getSupabaseClient()
        .from('proposals')
        .select(
          `
          *,
          client:clients (
            id,
            name,
            email,
            rep_full_name,
            billing_information (*)
          )
        `,
        )
        .eq('id', proposalId)
        .single();

      if (error || !proposal) {
        return {
          success: false,
          message: 'Proposal not found',
        };
      }

      // Transform billing information (it comes as array from Supabase)
      const transformedProposal: Proposal = {
        ...proposal,
        client: {
          ...proposal.client,
          billing_information: proposal.client.billing_information?.[0] || null,
        },
      };

      // Check if invoice should be created
      const shouldCreate = this.shouldCreateInvoice(transformedProposal);

      if (!shouldCreate.shouldCreate) {
        return {
          success: false,
          message: shouldCreate.reason || 'Invoice creation not needed',
        };
      }

      // Get or create inv.bg client
      const invBgClientId =
        await this.getOrCreateInvBgClient(transformedProposal);

      // Build invoice data
      const invoiceData = await this.buildInvoiceData(
        transformedProposal,
        shouldCreate.invoiceType!,
        invBgClientId,
      );

      // Create invoice via inv.bg API
      const invoiceResponse =
        await this.invBgService.createInvoice(invoiceData);

      // Update proposal with invoice ID (convert number to string for storage)
      const updateField =
        shouldCreate.invoiceType === 'initial'
          ? 'initial_invoice_id'
          : 'final_invoice_id';

      const { error: updateError } = await getSupabaseClient()
        .from('proposals')
        .update({ [updateField]: invoiceResponse.id.toString() })
        .eq('id', proposalId);

      if (updateError) {
        console.error(
          'Failed to update proposal with invoice ID:',
          updateError,
        );
        // Invoice was created, but we couldn't save the ID - log this for manual handling
      }

      // Send invoice email automatically
      try {
        await this.sendInvoiceEmail(
          transformedProposal,
          invoiceResponse,
          shouldCreate.invoiceType!,
        );
      } catch (emailError) {
        console.error('Failed to send invoice email:', emailError);
        // Don't fail the entire operation if email sending fails
      }

      return {
        success: true,
        invoiceId: invoiceResponse.id.toString(),
        invoiceType: shouldCreate.invoiceType!,
        message: `${shouldCreate.invoiceType === 'initial' ? 'Initial' : 'Final'} invoice created successfully`,
      };
    } catch (error: any) {
      console.error('Invoice generation error:', error);
      return {
        success: false,
        message: `Failed to generate invoice: ${error.message}`,
      };
    }
  }

  /**
   * Download invoice PDF from inv.bg v3 API
   */
  async downloadInvoicePdf(
    invoiceId: number,
    language: 'bg' | 'en' = 'en',
  ): Promise<Buffer> {
    try {
      return await this.invBgService.downloadInvoicePdf(invoiceId, language);
    } catch (error: any) {
      console.error('PDF download error:', error);
      throw new Error(`Failed to download invoice PDF: ${error.message}`);
    }
  }

  /**
   * Send invoice email with PDF attachment
   */
  private async sendInvoiceEmail(
    proposal: Proposal,
    invoiceResponse: any,
    invoiceType: 'initial' | 'final',
  ): Promise<void> {
    try {
      // Determine language based on client's country (Bulgarian for Bulgaria, English for others)
      const language: 'bg' | 'en' =
        proposal.client.billing_information?.country === 'Bulgaria'
          ? 'bg'
          : 'en';

      // Calculate amount for this invoice type based on payment type
      let amount: number;
      if (proposal.payment_type === 'full') {
        amount = proposal.price;
      } else {
        amount =
          invoiceType === 'initial'
            ? proposal.initial_payment || 0
            : proposal.price - (proposal.initial_payment || 0);
      }

      // Prepare email data
      const emailData: InvoiceEmailData = {
        clientName: proposal.client.name,
        clientEmail: proposal.client.email,
        invoiceNumber: invoiceResponse.number,
        invoiceType,
        projectTitle: proposal.project_title,
        proposalId: proposal.proposal_id,
        amount,
        currency: proposal.currency,
        companyName: process.env['COMPANY_NAME'] || 'Chainmatic Ltd.',
        language,
      };

      // Download invoice PDF
      let invoicePdf: Buffer | undefined;
      try {
        invoicePdf = await this.downloadInvoicePdf(
          invoiceResponse.id,
          language,
        );
      } catch (pdfError) {
        console.error('Failed to download invoice PDF for email:', pdfError);
        // Continue without PDF attachment
      }

      // Send email
      const emailSent = await this.emailService.sendInvoiceEmail(
        emailData,
        invoicePdf,
      );

      if (emailSent) {
        console.log(
          `Invoice email sent successfully to ${proposal.client.email} for invoice ${invoiceResponse.invoice_number}`,
        );
      } else {
        console.error(
          `Failed to send invoice email to ${proposal.client.email} for invoice ${invoiceResponse.invoice_number}`,
        );
      }
    } catch (error) {
      console.error('Error in sendInvoiceEmail:', error);
      throw error;
    }
  }
}
