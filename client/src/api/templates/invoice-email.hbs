<!DOCTYPE html>
<html lang="{{#if (eq language 'bg')}}bg{{else}}en{{/if}}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{#if (eq language 'bg')}}Фактура{{else}}Invoice{{/if}} #{{invoiceNumber}}</title>
  <style>
    /* Email best practices: Reset styles */
    body, table, td, p, a, li, blockquote {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    table, td {
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    img {
      -ms-interpolation-mode: bicubic;
    }

    body {
      margin: 0 !important;
      padding: 0 !important;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f9f9f9;
    }

    /* Main container with max-width best practice (600px) */
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #f9f9f9;
    }

    .container {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin: 20px;
    }

    .header {
      text-align: center;
      border-bottom: 3px solid #635bff;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }

    .logo {
      font-size: 28px;
      font-weight: bold;
      color: #635bff;
      margin-bottom: 10px;
    }

    .invoice-title {
      font-size: 24px;
      color: #333;
      margin: 20px 0;
    }

    /* Table-based layout for better email client support */
    .details-table {
      width: 100%;
      border-collapse: collapse;
      background-color: #f8f9fa;
      border-radius: 8px;
      margin: 20px 0;
      overflow: hidden;
    }

    .details-table td {
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
    }

    .details-table tr:last-child td {
      border-bottom: none;
    }

    .detail-label {
      font-weight: bold;
      color: #555;
      width: 40%;
      text-align: left;
    }

    .detail-value {
      color: #333;
      width: 60%;
      text-align: right;
    }

    .proposal-id {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      word-break: break-all;
      line-height: 1.4;
    }

    .amount {
      font-size: 16px;
      font-weight: bold;
      color: #7125bb;
    }

    .message {
      margin: 25px 0;
      padding: 20px;
      background-color: #f5f0ff;
      border-left: 4px solid #635bff;
      border-radius: 4px;
    }

    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      text-align: center;
      color: #666;
      font-size: 14px;
    }

    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #635bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 15px 0;
      font-weight: bold;
    }

    .button:hover {
      background-color: #5046e5;
    }

    /* Mobile optimizations */
    @media only screen and (max-width: 600px) {
      .email-container {
        width: 100% !important;
      }

      .container {
        margin: 10px !important;
        padding: 20px !important;
      }

      .details-table {
        margin: 15px 0 !important;
      }

      .details-table td {
        padding: 12px 15px !important;
      }

      .detail-label {
        width: 45% !important;
        text-align: left !important;
      }

      .detail-value {
        width: 55% !important;
        text-align: right !important;
        font-weight: 600;
      }

      .proposal-id {
        font-size: 12px !important;
        word-break: break-all;
      }

      .amount {
        font-size: 14px !important;
      }

      .logo {
        font-size: 24px !important;
      }

      .invoice-title {
        font-size: 20px !important;
      }
    }

    /* Extra small screens */
    @media only screen and (max-width: 400px) {
      .container {
        margin: 5px !important;
        padding: 15px !important;
      }

      .details-table td {
        padding: 10px 12px !important;
      }

      .proposal-id {
        font-size: 11px !important;
      }
    }
  </style>
</head>
<body>
<div class="email-container">
  <div class="container">
    <div class="header">
      <div class="logo">{{companyName}}</div>
      <h1 class="invoice-title">
        {{#if (eq language 'bg')}}
          Фактура #{{invoiceNumber}}
        {{else}}
          Invoice #{{invoiceNumber}}
        {{/if}}
      </h1>
    </div>

    <p>
      {{#if (eq language 'bg')}}
        Уважаеми {{clientName}},
      {{else}}
        Dear {{clientName}},
      {{/if}}
    </p>

    <p>
      {{#if (eq language 'bg')}}
        Изпращаме Ви фактурата за проект <strong>{{projectTitle}}</strong>.
      {{else}}
        Please find attached the invoice for project <strong>{{projectTitle}}</strong>.
      {{/if}}
    </p>

    <table class="details-table" cellpadding="0" cellspacing="0" border="0">
      <tr>
        <td class="detail-label">
          {{#if (eq language 'bg')}}
            Номер на фактура:
          {{else}}
            Invoice Number:
          {{/if}}
        </td>
        <td class="detail-value">{{invoiceNumber}}</td>
      </tr>

      <tr>
        <td class="detail-label">
          {{#if (eq language 'bg')}}
            Тип плащане:
          {{else}}
            Payment Type:
          {{/if}}
        </td>
        <td class="detail-value">
          {{#if (eq paymentType 'full')}}
            {{#if (eq language 'bg')}}
              Пълно плащане
            {{else}}
              Full Payment
            {{/if}}
          {{else}}
            {{#if (eq invoiceType 'initial')}}
              {{#if (eq language 'bg')}}
                Първоначално плащане (Фаза 1)
              {{else}}
                Initial Payment (Phase 1)
              {{/if}}
            {{else}}
              {{#if (eq language 'bg')}}
                Финално плащане (Фаза 2)
              {{else}}
                Final Payment (Phase 2)
              {{/if}}
            {{/if}}
          {{/if}}
        </td>
      </tr>

      <tr>
        <td class="detail-label">
          {{#if (eq language 'bg')}}
            Проект:
          {{else}}
            Project:
          {{/if}}
        </td>
        <td class="detail-value">{{projectTitle}}</td>
      </tr>

      <tr>
        <td class="detail-label">
          {{#if (eq language 'bg')}}
            Номер на предложение:
          {{else}}
            Proposal ID:
          {{/if}}
        </td>
        <td class="detail-value proposal-id">{{proposalId}}</td>
      </tr>

      <tr>
        <td class="detail-label">
          {{#if (eq language 'bg')}}
            Сума:
          {{else}}
            Amount:
          {{/if}}
        </td>
        <td class="detail-value amount">{{amount}} {{currency}}</td>
      </tr>
    </table>

    <div class="message">
      <p><strong>
        {{#if (eq language 'bg')}}
          Важна информация:
        {{else}}
          Important Information:
        {{/if}}
      </strong></p>
      <ul>
        {{#if (eq language 'bg')}}
          <li>Фактурата е приложена като PDF файл към този имейл</li>
          <li>Моля, запазете фактурата за Вашите счетоводни нужди</li>
          <li>При въпроси, моля свържете се с нас</li>
        {{else}}
          <li>The invoice is attached as a PDF file to this email</li>
          <li>Please keep this invoice for your accounting records</li>
          <li>Contact us if you have any questions</li>
        {{/if}}
      </ul>
    </div>

    <p>
      {{#if (eq language 'bg')}}
        Благодарим Ви за доверието!
      {{else}}
        Thank you for your business!
      {{/if}}
    </p>

    <div class="footer">
      <p><strong>{{companyName}}</strong></p>
      <p>
        {{#if (eq language 'bg')}}
          Този имейл е изпратен автоматично. Моля, не отговаряйте директно на този имейл.
        {{else}}
          This email was sent automatically. Please do not reply directly to this email.
        {{/if}}
      </p>
      <p>
        {{#if (eq language 'bg')}}
          За въпроси се свържете с нас на: <EMAIL>
        {{else}}
          For questions, contact us at: <EMAIL>
        {{/if}}
      </p>
    </div>
  </div>
</div>
</body>
</html>
