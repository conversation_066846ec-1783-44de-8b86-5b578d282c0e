-- Add payment_type column to proposals table
-- This determines whether the proposal uses split payments (initial + final) or full payment
ALTER TABLE proposals
    ADD COLUMN payment_type TEXT NOT NULL DEFAULT 'split' CHECK (payment_type IN ('split', 'full'));

-- Add comment for clarity
COMMENT ON COLUMN proposals.payment_type IS 'Payment structure: "split" for initial+final payments (default), "full" for single payment';

-- Create index for better performance when querying by payment type
CREATE INDEX idx_proposals_payment_type ON proposals (payment_type);
